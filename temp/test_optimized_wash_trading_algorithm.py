#!/usr/bin/env python3
"""
测试优化后的对敲算法
验证各项优化功能是否正常工作
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import pandas as pd
from datetime import datetime, timedelta
from backend.modules.contract_risk_analysis.optimizers.position_based_optimizer import PositionBasedOptimizer, CompletePosition

def create_test_data():
    """创建测试数据"""
    print("📊 创建测试数据...")
    
    # 模拟交易数据
    test_data = []
    base_time = datetime.now() - timedelta(hours=1)
    
    # 测试案例1: 典型的同账户对敲（一盈一亏）
    test_data.extend([
        {
            'member_id': 'user001',
            'contract_name': 'BTCUSDT',
            'position_id': 'pos001_long',
            'side': 1,  # 开多
            'deal_vol_usdt': 10000,
            'price': 50000,
            'volume': 0.2,
            'timestamp': base_time,
            'profit': 100,  # 盈利
            'fee_usdt': 10
        },
        {
            'member_id': 'user001',
            'contract_name': 'BTCUSDT',
            'position_id': 'pos001_short',
            'side': 3,  # 开空
            'deal_vol_usdt': 10000,
            'price': 50000,
            'volume': 0.2,
            'timestamp': base_time + timedelta(seconds=5),  # 5秒后
            'profit': -95,  # 亏损，基本对冲
            'fee_usdt': 10
        }
    ])
    
    # 测试案例2: 跨账户对敲（双盈情况）
    test_data.extend([
        {
            'member_id': 'user002',
            'contract_name': 'ETHUSDT',
            'position_id': 'pos002_long',
            'side': 1,
            'deal_vol_usdt': 5000,
            'price': 3000,
            'timestamp': base_time + timedelta(minutes=10),
            'profit': 5,  # 小盈利
            'fee_usdt': 5
        },
        {
            'member_id': 'user003',
            'contract_name': 'ETHUSDT',
            'position_id': 'pos003_short',
            'side': 3,
            'deal_vol_usdt': 5100,  # 略有差异，测试阶梯式容差
            'price': 3000,
            'timestamp': base_time + timedelta(minutes=10, seconds=3),
            'profit': 8,  # 小盈利，测试同边情况
            'fee_usdt': 5
        }
    ])
    
    # 测试案例3: 大额交易对敲
    test_data.extend([
        {
            'member_id': 'user004',
            'contract_name': 'BTCUSDT',
            'position_id': 'pos004_long',
            'side': 1,
            'deal_vol_usdt': 150000,  # 大额交易
            'price': 50000,
            'timestamp': base_time + timedelta(minutes=20),
            'profit': -500,
            'fee_usdt': 150
        },
        {
            'member_id': 'user005',
            'contract_name': 'BTCUSDT',
            'position_id': 'pos005_short',
            'side': 3,
            'deal_vol_usdt': 152000,  # 测试大额容差
            'price': 50000,
            'timestamp': base_time + timedelta(minutes=20, seconds=8),
            'profit': 480,  # 基本对冲
            'fee_usdt': 152
        }
    ])
    
    return pd.DataFrame(test_data)

def create_complete_positions(df):
    """创建CompletePosition对象用于测试"""
    positions = {}
    
    for _, row in df.iterrows():
        pos = CompletePosition(
            position_id=row['position_id'],
            member_id=row['member_id'],
            contract_name=row['contract_name'],
            first_open_time=row['timestamp'],
            last_open_time=row['timestamp'],
            total_open_amount=row['deal_vol_usdt'],
            total_open_volume=row.get('volume', 0),
            avg_open_price=row['price'],
            open_trades_count=1,
            primary_side=row['side'],
            first_close_time=row['timestamp'] + timedelta(minutes=30),
            last_close_time=row['timestamp'] + timedelta(minutes=30),
            total_close_amount=row['deal_vol_usdt'],
            total_close_volume=row.get('volume', 0),
            avg_close_price=row['price'] + (10 if row['side'] == 1 else -10),
            close_trades_count=1,
            is_completed=True,
            total_duration_minutes=30,
            real_profit=row['profit'],
            calculated_profit=row['profit'],
            is_quick_trade=False,
            is_scalping=False,
            add_position_count=0,
            reduce_position_count=0,
            risk_score=0.0,
            abnormal_flags=[],
            leverage=10.0,
            total_fee=row.get('fee_usdt', 0)
        )
        positions[row['position_id']] = pos
    
    return positions

def test_optimized_algorithms():
    """测试优化后的算法功能"""
    print("🚀 开始测试优化后的对敲算法...")
    
    # 创建测试数据
    df = create_test_data()
    positions = create_complete_positions(df)
    
    # 初始化优化器
    optimizer = PositionBasedOptimizer()
    optimizer.complete_positions = positions
    
    print("\n📋 测试配置信息获取...")
    config_info = optimizer.get_config_info()
    print(f"算法版本: {config_info['version']}")
    print(f"优化特性数量: {len(config_info['optimization_features'])}")
    print("主要优化特性:")
    for feature in config_info['optimization_features'][:3]:
        print(f"  - {feature}")
    
    print("\n🔍 测试对敲检测...")
    wash_results = optimizer.optimized_wash_trading_detection()
    
    print(f"\n📊 检测结果统计:")
    print(f"总检测结果: {len(wash_results)}")
    
    if wash_results:
        # 按风险等级统计
        risk_levels = {}
        for result in wash_results:
            level = result.get('severity', 'Unknown')
            risk_levels[level] = risk_levels.get(level, 0) + 1
        
        print("风险等级分布:")
        for level, count in risk_levels.items():
            print(f"  {level}: {count}")
        
        print("\n🔍 详细结果分析:")
        for i, result in enumerate(wash_results[:3], 1):  # 显示前3个结果
            print(f"\n结果 {i}:")
            print(f"  检测类型: {result.get('detection_method', 'N/A')}")
            print(f"  风险等级: {result.get('severity', 'N/A')}")
            print(f"  综合评分: {result.get('wash_score', 0):.4f}")
            print(f"  盈亏对冲评分: {result.get('profit_hedge_score', 0):.4f}")
            print(f"  时间匹配评分: {result.get('time_match_score', 0):.4f}")
            print(f"  金额匹配评分: {result.get('amount_match_score', 0):.4f}")
            print(f"  时间差: {result.get('time_diff_seconds', 0):.1f}秒")
            print(f"  总盈亏: {result.get('total_profit', 0):.2f}")
    
    print("\n✅ 算法测试完成!")
    return wash_results

def test_individual_functions():
    """测试各个优化函数"""
    print("\n🧪 测试各个优化函数...")
    
    optimizer = PositionBasedOptimizer()
    
    # 测试阶梯式金额容差
    print("\n📏 测试阶梯式金额容差:")
    test_amounts = [(100, 105), (1000, 1050), (10000, 10200), (100000, 101000)]
    for amount_a, amount_b in test_amounts:
        tolerance = optimizer._calculate_amount_tolerance(amount_a, amount_b)
        match_score = optimizer._calculate_optimized_amount_match_score(amount_a, amount_b)
        print(f"  金额 {amount_a} vs {amount_b}: 容差={tolerance:.2f}, 评分={match_score:.4f}")
    
    # 测试时间匹配评分
    print("\n⏰ 测试时间匹配评分:")
    test_time_diffs = [1, 5, 10, 15, 30]
    for time_diff in test_time_diffs:
        score = optimizer._calculate_optimized_time_match_score(time_diff)
        print(f"  时间差 {time_diff}秒: 评分={score:.4f}")
    
    # 测试盈亏对冲评分
    print("\n💰 测试盈亏对冲评分:")
    test_profits = [
        (100, -95, 10000, 10000),  # 一盈一亏
        (5, 8, 5000, 5000),        # 双盈
        (-10, -12, 8000, 8000),    # 双亏
        (0, 0, 5000, 5000)         # 无盈亏
    ]
    for profit_a, profit_b, amount_a, amount_b in test_profits:
        score = optimizer._calculate_optimized_profit_hedge_score(profit_a, profit_b, amount_a, amount_b)
        print(f"  盈亏 {profit_a} vs {profit_b}: 评分={score:.4f}")
    
    # 测试风险等级计算
    print("\n🎯 测试风险等级计算:")
    test_scores = [
        (0.95, 0.92, 0.85),  # 应该是Critical
        (0.88, 0.80, 0.70),  # 应该是High
        (0.75, 0.70, 0.60),  # 应该是Medium
        (0.55, 0.50, 0.40),  # 应该是Low
        (0.30, 0.25, 0.20)   # 应该是Minimal
    ]
    for wash_score, profit_score, time_score in test_scores:
        level = optimizer._calculate_risk_level(wash_score, profit_score, time_score)
        print(f"  评分 {wash_score:.2f}/{profit_score:.2f}/{time_score:.2f}: 等级={level}")

if __name__ == "__main__":
    print("🔬 对敲算法优化测试")
    print("=" * 50)
    
    # 测试主要功能
    results = test_optimized_algorithms()
    
    # 测试各个函数
    test_individual_functions()
    
    print("\n" + "=" * 50)
    print("✅ 所有测试完成!")
